import { supabase } from "../lib/client";
import { rpc, views } from "../lib/apiConfig";
import type {
  AddCourseResultType,
  CourseDetailsResultType,
  CoursesParams,
  DuplicateCourseResultType,
  DuplicateCourseValueType,
  TopicDataType,
  addCourseValueType,
  SectionViewResultType,
  ViewResourcePageType,
  ResourceFileRequest,
  ResourceFileResult,
  CheckpointQuizResponse,
  CheckPointRequest,
  CheckPointResponse,
  ResourceVideoRequest,
  AddPageForm,
  AddPageResponse,
  SectionListResponse,
  EditCourseValueType,
  EditCourseResultType,
  AddCourseVideoRequest,
  AddCourseVideoResponse,
  UpdateCheckpointResponse,
  PublishCourseResponse,
  UpdateCheckPointRequest,
  LoginUserData,
  CheckPointsResponse,
  CommentRequest,
  CommentResponse,
  deleteResourceRequest,
  deleteResourceResponse,
  DeleteCourseLinkedResources,
  CourseDetailsRequest,
  ErrorType,
  DeleteCourseRequest,
  CourseDeleteResponse,
  AddFolderRequest,
  folderListResponse,
  AddFolderResponse,
  PPTCheckPointRequest,
  CoursePlanResponse,
  GetCourseByCategoryReq,
  GetCourseByCategoryResponse,
  AddFolderFromLibraryRequest,
  DeleteSectionRequest,
  FolderResourecList,
  AddSectionRequest,
  AddSectionResponse,
  OrderSectionRequest,
  OrderFolderRequest,
  OrderResourceRequest,
  DeleteFolderRequest,
  RenameFolderRequest,
} from "@/types";
import type { TreeDataItem } from "@/components/ui/tree";
import { ORG_KEY } from "@/lib/constants";

interface UseCourseReturn {
  getCourseList: (topicId?: string | null) => Promise<CoursesParams[]>;
  listCourses: (topicId?: string | null) => Promise<CoursesParams[]>;
  getCourseListForEnrollments: (
    topicId?: string | null,
  ) => Promise<CoursesParams[]>;
  addCourse: (params: addCourseValueType) => Promise<AddCourseResultType>;
  editCourse: (params: EditCourseValueType) => Promise<EditCourseResultType>;
  duplicateCourse: (
    params: DuplicateCourseValueType,
  ) => Promise<DuplicateCourseResultType>;
  deleteCourse: (
    courseData: DeleteCourseRequest,
  ) => Promise<CourseDeleteResponse>;
  courseDetails: (
    queryParams: CourseDetailsRequest,
  ) => Promise<CourseDetailsResultType[]>;
  resourceView: (sectionId: string) => Promise<SectionViewResultType | []>;
  viewResourcePage: (
    instanceId: string,
    type: string,
    course_module_id?: string,
  ) => Promise<ViewResourcePageType | []>;
  convertDataToTreeNode: (
    apiData: TopicDataType[],
    parentKey?: string,
  ) => TreeDataItem[];
  resourceInsert: (
    params: ResourceFileRequest | ResourceVideoRequest,
    resourceType: string,
  ) => Promise<ResourceFileResult>;
  getCheckPointQuizList: () => Promise<CheckpointQuizResponse[]>;
  getCheckPointsList: (courseModuleId: string) => Promise<CheckPointsResponse>;
  addCheckPoint: (params: CheckPointRequest[]) => Promise<CheckPointResponse>;
  addPPTCheckPoint: (
    params: PPTCheckPointRequest[],
  ) => Promise<CheckPointResponse>;
  addPageContent: (params: AddPageForm) => Promise<AddPageResponse>;
  getSectionList: (
    queryParams: CourseDetailsRequest,
  ) => Promise<SectionListResponse[]>;
  addCourseVideo: (
    param?: AddCourseVideoRequest,
  ) => Promise<AddCourseVideoResponse>;
  publishCourse: (
    course_id: string,
    status: string,
  ) => Promise<PublishCourseResponse>;
  updateCheckpoint: (
    params: UpdateCheckPointRequest,
  ) => Promise<UpdateCheckpointResponse>;
  getComments: (params: CommentRequest) => Promise<CommentResponse[]>;
  deleteResourceLinked: (
    params?: deleteResourceRequest,
  ) => Promise<deleteResourceResponse>;
  deleteCourseLinkedResource: (
    params?: DeleteCourseLinkedResources,
  ) => Promise<deleteResourceResponse>;
  addFolder: (params: AddFolderRequest) => Promise<AddFolderResponse>;
  getFolderList: (
    course_id: string,
    section_id: string,
  ) => Promise<folderListResponse[]>;
  getPlanList: (
    course_or_module_id: string,
    org_id: string,
  ) => Promise<CoursePlanResponse>;

  getCourseByCategory: (
    params: GetCourseByCategoryReq,
  ) => Promise<GetCourseByCategoryResponse[]>;

  addFolderFromLibrary: (
    params: AddFolderFromLibraryRequest,
  ) => Promise<AddFolderResponse>;
  listFolderFromLibrary: (org_id: string) => Promise<folderListResponse[]>;
  deleteSection: (
    params: DeleteSectionRequest,
  ) => Promise<CourseDeleteResponse>;
  getFolderResources: (
    folder_id: string,
    course_id: string,
    section_id: string,
  ) => Promise<FolderResourecList[]>;
  addSection: (params: AddSectionRequest) => Promise<AddSectionResponse>;
  orderSection: (params: OrderSectionRequest) => Promise<AddSectionResponse>;
  orderFolder: (params: OrderFolderRequest) => Promise<AddSectionResponse>;
  orderResource: (params: OrderResourceRequest) => Promise<AddSectionResponse>;
  deleteFolder: (params: DeleteFolderRequest) => Promise<deleteResourceResponse>;
  renameFolder: (params: RenameFolderRequest) => Promise<deleteResourceResponse>;
}

const useCourse = (): UseCourseReturn => {
  async function getCourseList(
    topicId?: string | null,
  ): Promise<CoursesParams[]> {
    try {
      const courseView = views?.course ?? "";
      const org_id = localStorage.getItem("orgId");
      const exeQuery = supabase
        .from(courseView)
        .select()
        .eq("org_id", org_id)
        .order("short_name", { ascending: true });

      if (topicId !== null && topicId !== "" && topicId !== undefined) {
        await exeQuery.eq("category_id", topicId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as CoursesParams[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function listCourses(
    topicId?: string | null,
  ): Promise<CoursesParams[]> {
    try {
      const courseView = views?.course ?? "";
      const org_id = localStorage.getItem("orgId");
      const exeQuery = supabase
        .from(courseView)
        .select()
        .eq("org_id", org_id)
        .order("created_at", { ascending: false });

      if (topicId !== null && topicId !== "" && topicId !== undefined) {
        await exeQuery.eq("category_id", topicId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as CoursesParams[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getCourseListForEnrollments(
    topicId?: string | null,
  ): Promise<CoursesParams[]> {
    try {
      const courseView = views?.course ?? "";
      const org_id = localStorage.getItem("orgId");
      const exeQuery = supabase
        .from(courseView)
        .select()
        .eq("org_id", org_id)
        .eq("status", "Published")
        .eq("is_expired", "FALSE")
        .order("short_name", { ascending: true });

      if (topicId !== null && topicId !== "" && topicId !== undefined) {
        await exeQuery.eq("category_id", topicId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as CoursesParams[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addCourse(
    params: addCourseValueType,
  ): Promise<AddCourseResultType> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.insertCourse,
        params,
      )) as {
        data: AddCourseResultType;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as AddCourseResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function duplicateCourse(
    params: DuplicateCourseValueType,
  ): Promise<DuplicateCourseResultType> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.copyCourseDetails,
        params,
      )) as { data: DuplicateCourseResultType; error: ErrorType | null };

      if (error) {
        throw new Error(error.details);
      }
      return data as DuplicateCourseResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteCourse(
    courseData: DeleteCourseRequest,
  ): Promise<CourseDeleteResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteCourse,
        courseData,
      )) as {
        data: CourseDeleteResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CourseDeleteResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function courseDetails(
    queryParams: CourseDetailsRequest,
  ): Promise<CourseDetailsResultType[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCourseDetails,
        queryParams,
      )) as {
        data: CourseDetailsResultType[];
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }

      // Check if data is an array and has at least one item
      if (Array.isArray(data) && data.length > 0) {
        // Return the first item as CourseDetailsResultType
        return data as CourseDetailsResultType[];
      }

      // Return null if no data found
      return [];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function resourceView(
    sectionId: string,
  ): Promise<SectionViewResultType | []> {
    try {
      const userDetails = localStorage.getItem("userDetails");

      if (userDetails !== null && userDetails !== undefined) {
        const userInfo = JSON.parse(userDetails) as LoginUserData;
        const requestBody = {
          user_id: userInfo.id, //localStorage.getItem("userId"), need to dynamic
          section_id: sectionId,
        };
        const { data, error } = await supabase.rpc<string, null>(
          rpc.getSectionDetails,
          requestBody,
        );
        if (error) {
          throw new Error(error.details);
        }

        if (Array.isArray(data) && data.length > 0) {
          // Return the first item as CourseDetailsResultType
          return data[0] as SectionViewResultType;
        }
      }
      return [];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function viewResourcePage(
    type: string,
    instanceId: string,
    course_module_id?: string,
  ): Promise<ViewResourcePageType | []> {
    const requestBody: {
      org_id?: string;
      url_id?: string;
      file_id?: string;
      page_id?: string;
      quiz_id?: string;
      course_module_id?: string;
      user_id?: string;
    } = {};
    const orgId = localStorage.getItem("orgId");
    if (orgId !== null && type != "Quiz") {
      requestBody.org_id = orgId;
    }

    let fromInstance = "";

    if (type == "Url") {
      fromInstance = "fn_get_course_resource_url";
      const userDetails = localStorage.getItem("userDetails");
      if (userDetails !== null && userDetails !== undefined) {
        const userInfo = JSON.parse(userDetails) as LoginUserData;
        requestBody.user_id = userInfo.id;
      }
      requestBody.url_id = instanceId;
    } else if (type == "Page") {
      fromInstance = "fn_get_view_resource_page";

      requestBody.page_id = instanceId;
    } else if (type == "Quiz") {
      fromInstance = "get_questions_of_quiz";
      // You can set other properties for Quiz here
      requestBody.quiz_id = instanceId;
    } else if (type == "File") {
      fromInstance = "fn_get_view_resource_file";

      requestBody.file_id = instanceId;
    }
    requestBody.course_module_id = course_module_id as string;

    try {
      if (
        fromInstance !== null &&
        fromInstance !== undefined &&
        fromInstance !== ""
      ) {
        const { data, error } = await supabase.rpc<string, null>(
          fromInstance,
          requestBody,
        );
        if (error) {
          throw new Error(error.details);
        }

        if (Array.isArray(data) && data.length > 0) {
          // Return the first item as CourseDetailsResultType
          return data[0] as ViewResourcePageType;
        } else {
          return data as ViewResourcePageType;
        }
      } else {
        return [];
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function editCourse(
    params: EditCourseValueType,
  ): Promise<EditCourseResultType> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateCourse,
        params,
      )) as {
        data: EditCourseValueType;
        error: ErrorType | null;
      };

      if (error) {
        throw error;
      }
      return data as EditCourseResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  const convertDataToTreeNode = (
    apiData: TopicDataType[],
    parentKey = "",
  ): TreeDataItem[] => {
    return apiData.map((item, index) => {
      const key = parentKey === "" ? `${index}` : `${parentKey}-${index}`;
      const children = item.children
        ? convertDataToTreeNode(item.children, key)
        : [];

      return {
        key,
        label: item.label,
        value: item.value,
        children,
        data: item.value,
        is_premium: item.is_premium,
      };
    });
  };
  async function resourceInsert(
    params: ResourceFileRequest | ResourceVideoRequest,
    resourceType: string,
  ): Promise<ResourceFileResult> {
    try {
      let rpcFunctionName = "insert_file";

      if (resourceType === "4") {
        rpcFunctionName = "insert_url";
      }
      const { data, error } = (await supabase.rpc(rpcFunctionName, params)) as {
        data: ResourceFileResult;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as ResourceFileResult;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCheckPointsList(
    courseModuleId: string,
  ): Promise<CheckPointsResponse> {
    try {
      const requestBody = {
        org_id: localStorage.getItem(ORG_KEY),
        course_module_id: courseModuleId,
      };
      const { data, error } = await supabase.rpc<string, null>(
        views?.checkPointsList,
        requestBody,
      );
      if (error) {
        throw new Error(error.details);
      }
      //if (Array.isArray(data) && data.length > 0) {

      return data as CheckPointsResponse;
      // }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCheckPointQuizList(): Promise<CheckpointQuizResponse[]> {
    try {
      const cpQuizView = views?.checkPointQuiz ?? "";
      const org_id = localStorage.getItem("orgId");
      const course_id = localStorage.getItem("courseId");

      const exeQuery = supabase
        .from(cpQuizView)
        .select()
        .eq("org_id", org_id)
        .eq("course_id", course_id);

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as CheckpointQuizResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addCheckPoint(
    param?: CheckPointRequest[],
  ): Promise<CheckPointResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addCheckPoint,
        param,
      )) as { data: CheckPointResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as CheckPointResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addPPTCheckPoint(
    param?: PPTCheckPointRequest[],
  ): Promise<CheckPointResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addCheckPoint,
        param,
      )) as { data: CheckPointResponse; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as CheckPointResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addPageContent(param?: AddPageForm): Promise<AddPageResponse> {
    try {
      const { data, error } = (await supabase.rpc(rpc.insertPage, param)) as {
        data: AddPageResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddPageResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getSectionList(
    reqParams: CourseDetailsRequest,
  ): Promise<SectionListResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCourseDetails,
        reqParams,
      )) as {
        data: SectionListResponse[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }

      return data as SectionListResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addCourseVideo(
    param?: AddCourseVideoRequest,
  ): Promise<AddCourseVideoResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addCourseResource,
        param,
      )) as {
        data: AddCourseVideoResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddPageResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function publishCourse(
    course_id: string,
    status: string,
  ): Promise<PublishCourseResponse> {
    const param = {
      course_id: course_id,
      status: status,
    };
    try {
      const { data, error } = (await supabase.rpc(
        rpc.publishCourse,
        param,
      )) as {
        data: PublishCourseResponse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as PublishCourseResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function updateCheckpoint(
    param?: UpdateCheckPointRequest,
  ): Promise<UpdateCheckpointResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateCheckpointStatus,
        param,
      )) as {
        data: UpdateCheckpointResponse;
        error: ErrorType | null;
      };

      if (error) {
        throw new Error(error.details);
      }
      return data as UpdateCheckpointResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getComments(
    params?: CommentRequest,
  ): Promise<CommentResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCommentsOfInstance,
        params,
      )) as {
        data: CommentResponse[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CommentResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteResourceLinked(
    params?: deleteResourceRequest,
  ): Promise<deleteResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteResourceLinked,
        params,
      )) as {
        data: deleteResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as deleteResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteCourseLinkedResource(
    params?: DeleteCourseLinkedResources,
  ): Promise<deleteResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteLinkedResources,
        params,
      )) as {
        data: deleteResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as deleteResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addFolder(
    params?: AddFolderRequest,
  ): Promise<AddFolderResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        "add_resource_folder",
        params,
      )) as {
        data: AddFolderResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddFolderResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getFolderList(
    course_id?: string | null,
    section_id?: string | null,
  ): Promise<folderListResponse[]> {
    try {
      const foldersList = views?.folderList ?? "";
      const org_id = localStorage.getItem("orgId");
      const exeQuery = supabase
        .from(foldersList)
        .select()
        .eq("org_id", org_id)
        .eq("course_id", course_id);

      await exeQuery.eq("section_id", section_id);

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as folderListResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getPlanList(
    course_id: string,
    org_id: string,
  ): Promise<CoursePlanResponse> {
    try {
      const param = {
        course_or_module_id: course_id,
        org_id: org_id,
      };
      const { data, error } = (await supabase.rpc(
        rpc.getPlanListForCourse,
        param,
      )) as {
        data: CoursePlanResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as CoursePlanResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getCourseByCategory(
    params?: GetCourseByCategoryReq,
  ): Promise<GetCourseByCategoryResponse[]> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getCoursesByCategory,
        params,
      )) as {
        data: GetCourseByCategoryResponse[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as GetCourseByCategoryResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addFolderFromLibrary(
    params?: AddFolderFromLibraryRequest,
  ): Promise<AddFolderResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.createFolder,
        params,
      )) as {
        data: AddFolderResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw error;
      }
      return data as AddFolderResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function listFolderFromLibrary(
    org_id: string,
  ): Promise<folderListResponse[]> {
    try {
      const param = {
        org_id: org_id,
      };
      const { data, error } = (await supabase.rpc(
        rpc.getFoldersList,
        param,
      )) as {
        data: folderListResponse[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as folderListResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function deleteSection(
    sectionData: DeleteSectionRequest,
  ): Promise<CourseDeleteResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteSection,
        sectionData,
      )) as {
        data: CourseDeleteResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw error;
      }
      return data as CourseDeleteResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getFolderResources(
    folder_id: string,
    course_id: string,
    section_id: string,
  ): Promise<FolderResourecList[]> {
    const org_id = localStorage.getItem("orgId");
    const param = {
      org_id: org_id,
      folder_id: folder_id,
      course_id: course_id,
      section_id: section_id,
    };
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getFolderResource,
        param,
      )) as {
        data: FolderResourecList[];
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as FolderResourecList[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function addSection(
    params: AddSectionRequest,
  ): Promise<AddSectionResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.addCourseSection,
        params,
      )) as {
        data: AddSectionResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddSectionResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function orderSection(
    params: OrderSectionRequest,
  ): Promise<AddSectionResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.orderSection,
        params,
      )) as {
        data: AddSectionResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddSectionResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function orderFolder(
    params: OrderFolderRequest,
  ): Promise<AddSectionResponse> {
    try {
      const { data, error } = (await supabase.rpc(rpc.orderFolder, params)) as {
        data: AddSectionResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddSectionResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function orderResource(
    params: OrderResourceRequest,
  ): Promise<AddSectionResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.ordeResource,
        params,
      )) as {
        data: AddSectionResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as AddSectionResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function deleteFolder(
    params: DeleteFolderRequest,
  ): Promise<deleteResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.deleteFolder,
        params,
      )) as {
        data: deleteResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as deleteResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function renameFolder(
    params: RenameFolderRequest,
  ): Promise<deleteResourceResponse> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.renameFolder,
        params,
      )) as {
        data: deleteResourceResponse;
        error: ErrorType | null;
      };
      if (error) {
        throw new Error(error.details);
      }
      return data as deleteResourceResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    getFolderList,
    getCourseList,
    listCourses,
    getCourseListForEnrollments,
    convertDataToTreeNode,
    addCourse,
    duplicateCourse,
    deleteCourse,
    courseDetails,
    resourceView,
    viewResourcePage,
    resourceInsert,
    getCheckPointQuizList,
    getCheckPointsList,
    addCheckPoint,
    addPPTCheckPoint,
    addPageContent,
    getSectionList,
    editCourse,
    addCourseVideo,
    publishCourse,
    updateCheckpoint,
    getComments,
    deleteResourceLinked,
    deleteCourseLinkedResource,
    addFolder,
    getPlanList,
    getCourseByCategory,
    addFolderFromLibrary,
    listFolderFromLibrary,
    deleteSection,
    getFolderResources,
    addSection,
    orderSection,
    orderFolder,
    orderResource,
    deleteFolder,
    renameFolder,
  };
};

export default useCourse;
