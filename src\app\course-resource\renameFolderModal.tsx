"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { useTranslation } from "react-i18next";
import useCourse from "@/hooks/useCourse";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { ORG_KEY } from "@/lib/constants";
import type {
  RenameFolderRequest,
  LogUserActivityRequest,
  ToastType,
  LoginUserData,
} from "@/types";

interface RenameFolderModalProps {
  folderId: string;
  currentFolderName: string;
  courseId: string;
  sectionId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

export default function RenameFolderModal({
  folderId,
  currentFolderName,
  courseId,
  sectionId,
  onCancel,
  onSuccess,
}: RenameFolderModalProps): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { renameFolder } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const [newFolderName, setNewFolderName] = useState(currentFolderName);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    try {
      await updateUserActivity(params);
    } catch (error) {
      console.error("Error logging user activity:", error);
    }
  };

  const validateFolderName = (name: string): boolean => {
    if (name.trim().length === 0) {
      setError(t("validation.folder_name_required"));
      return false;
    }
    if (name.trim().length < 2) {
      setError(t("validation.folder_name_min_length"));
      return false;
    }
    if (name.trim().length > 50) {
      setError(t("validation.folder_name_max_length"));
      return false;
    }
    setError("");
    return true;
  };

  const handleRename = async (): Promise<void> => {
    const trimmedName = newFolderName.trim();

    if (!validateFolderName(trimmedName)) return;

    if (trimmedName === currentFolderName.trim()) {
      setError(t("validation.folder_name_unchanged"));
      return;
    }

    const orgId = localStorage.getItem(ORG_KEY);
    const userDetails = localStorage.getItem("userDetails");

    if (orgId == null || userDetails == null) return;

    const userInfo = JSON.parse(userDetails) as LoginUserData;

    const renameParams: RenameFolderRequest = {
      folder_id: folderId,
      org_id: orgId,
      course_id: courseId,
      section_id: sectionId,
      folder_name: trimmedName,
    };

    setIsLoading(true);
    try {
      const response = await renameFolder(renameParams);

      if (response.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.folder_renamed"),
        });

        const activityParams: LogUserActivityRequest = {
          activity_type: "Course",
          screen_name: "Course Module",
          action_details: `Folder renamed from "${currentFolderName}" to "${trimmedName}"`,
          target_id: folderId,
          log_result: "Success",
          org_id: orgId,
          user_id: userInfo.id,
        };

        await updateLogUserActivity(activityParams);
        onSuccess();
      } else {
        throw new Error("Failed to rename folder");
      }
    } catch (error) {
      console.error("Error renaming folder:", error);

      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.folder_rename_failed"),
      });

      const activityParams: LogUserActivityRequest = {
        activity_type: "Course",
        screen_name: "Course Module",
        action_details: `Failed to rename folder "${currentFolderName}"`,
        target_id: folderId,
        log_result: "Error",
        org_id: orgId,
        user_id: userInfo?.id,
      };

      await updateLogUserActivity(activityParams);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = (): void => {
    onCancel();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setNewFolderName(value);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === "Enter") {
      void handleRename();
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <div className="space-y-2">
          <label
            htmlFor="folderName"
            className="block text-sm font-medium text-gray-700"
          >
            {t("form.folder_name")} <span className="text-red-500">*</span>
          </label>
          <Input
            id="folderName"
            type="text"
            value={newFolderName}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={t("form.folder_name_placeholder")}
            maxLength={50}
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          {t("buttons.cancel")}
        </Button>
        <Button
          type="button"
          onClick={() => void handleRename()}
          disabled={
            isLoading ||
            !(error.length === 0) ||
            newFolderName.trim().length === 0
          }
        >
          {isLoading ? t("buttons.saving") : t("buttons.save")}
        </Button>
      </div>
    </div>
  );
}
