"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useTranslation } from "react-i18next";
import useCourse from "@/hooks/useCourse";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { ORG_KEY } from "@/lib/constants";
import type {
  DeleteFolderRequest,
  LoginUserData,
  LogUserActivityRequest,
  ToastType,
} from "@/types";

interface DeleteFolderModalProps {
  folderId: string;
  folderName: string;
  courseId: string;
  sectionId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

export default function DeleteFolderModal({
  folderId,
  folderName,
  courseId,
  sectionId,
  onCancel,
  onSuccess,
}: DeleteFolderModalProps): React.JSX.Element {
  const { t } = useTranslation();
  const { toast } = useToast() as ToastType;
  const { deleteFolder } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const [isLoading, setIsLoading] = useState(false);

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    try {
      await updateUserActivity(params);
    } catch (error) {
      console.error("Error logging user activity:", error);
    }
  };

  const handleDelete = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const orgId = localStorage.getItem(ORG_KEY);
      const userDetails = localStorage.getItem("userDetails");

      if (orgId == null || userDetails == null) return;

      const userInfo = JSON.parse(userDetails) as LoginUserData;

      const deleteParams: DeleteFolderRequest = {
        folder_id: folderId,
        org_id: orgId as string,
        course_id: courseId,
        section_id: sectionId,
      };

      const response = await deleteFolder(deleteParams);

      if (response.status === "success") {
        toast({
          variant: SUCCESS_MESSAGES.toast_variant_default,
          title: t("successMessages.toast_success_title"),
          description: t("successMessages.folder_deleted"),
        });

        // Log user activity
        const activityParams: LogUserActivityRequest = {
          activity_type: "Course",
          screen_name: "Course Module",
          action_details: `Folder "${folderName}" deleted successfully`,
          target_id: folderId,
          log_result: "Success",
          org_id: orgId as string,
          user_id: userInfo.id,
        };
        await updateLogUserActivity(activityParams);

        onSuccess();
      } else {
        throw new Error("Failed to delete folder");
      }
    } catch (error) {
      console.error("Error deleting folder:", error);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.folder_delete_failed"),
      });

      // Log error activity
      const orgId = localStorage.getItem(ORG_KEY);
      const userDetails = localStorage.getItem("userDetails");

      if (orgId == null || userDetails == null) return;

      const userInfo = JSON.parse(userDetails) as LoginUserData;
      if (orgId != null) {
        const activityParams: LogUserActivityRequest = {
          activity_type: "Course",
          screen_name: "Course Module",
          action_details: `Failed to delete folder "${folderName}"`,
          target_id: folderId,
          log_result: "Error",
          org_id: orgId,
          user_id: userInfo.id as string,
        };
        await updateLogUserActivity(activityParams);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = (): void => {
    onCancel();
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <p className="mt-2">{t("confirmationModal.delete_folder_message")}</p>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          {t("buttons.cancel")}
        </Button>
        <Button
          type="button"
          variant="default"
          onClick={() => void handleDelete()}
          disabled={isLoading}
        >
          {isLoading ? t("buttons.deleting") : t("buttons.delete")}
        </Button>
      </div>
    </div>
  );
}
